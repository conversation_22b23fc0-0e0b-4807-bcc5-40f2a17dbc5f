import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Divider,
  IconButton,
  Dialog,
} from "@mui/material";
import AddBusinessCategoryModal from "./components/addBusinessCategory.component";
import ServicesDisplay from "./components/servicesDisplay.component";
import AddIcon from "@mui/icons-material/Add";
import EditBusinessNameModal from "../../components/editBusinessName/editBusinessName.component";

interface DemoScreenProps {
  title?: string;
}

const DemoScreen: React.FC<DemoScreenProps> = ({ title }) => {
  useEffect(() => {
    if (title) {
      document.title = title;
    }
  }, [title]);

  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [businessName, setBusinessName] = useState<string>(
    "Sri Eye Care Hospital - Best Ophthalmologist in RT Nagar | Best Cataract Surgeon in Bangalore"
  );

  const handleOpenBusinessNameModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseBusniessNameModal = () => {
    setIsModalOpen(false);
  };

  const handleOpenAddCategoryModal = () => {
    setIsAddCategoryModalOpen(true);
  };

  const handleCloseAddCategoryModal = () => {
    setIsAddCategoryModalOpen(false);
  };

  const handleAddCategory = (categoryName: string) => {
    console.log("Added category:", categoryName);
    setIsAddCategoryModalOpen(false);
  };

  return (
    <Box sx={{ p: 3, maxWidth: "800px", margin: "0 auto" }}>
      <Typography variant="h4" gutterBottom>
        Business Category Demo
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        This is a demo page to showcase the business category modal
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Demo Controls
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenAddCategoryModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Open Add Business Category Modal
            </Button>
          </Box>

          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenBusinessNameModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Edit Business
            </Button>
          </Box>
        </CardContent>
      </Card>

      <AddBusinessCategoryModal
        open={isAddCategoryModalOpen}
        onClose={handleCloseAddCategoryModal}
        onAddCategory={handleAddCategory}
      />

      {/* Edit Business Name Modal */}
      <EditBusinessNameModal
        open={isModalOpen}
        onClose={handleCloseBusniessNameModal}
        businessId={1} // Demo business ID
        currentBusinessName={businessName}
        onSuccess={() => {
          // In a real app, we would fetch the updated business data
          // For demo purposes, we'll just update the local state with the form value
          const inputElement = document.getElementById(
            "businessName"
          ) as HTMLInputElement;
          setBusinessName(inputElement?.value || businessName);
        }}
      />
    </Box>
  );
};

export default DemoScreen;
