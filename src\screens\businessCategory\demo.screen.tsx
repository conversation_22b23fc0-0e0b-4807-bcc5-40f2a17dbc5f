import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>ton,
  Typography,
  Card,
  CardContent,
  Divider,
  IconButton,
  Dialog,
} from "@mui/material";
import AddBusinessCategoryModal from "./components/addBusinessCategory.component";
import ServicesDisplay from "./components/servicesDisplay.component";
import AddIcon from "@mui/icons-material/Add";

interface DemoScreenProps {
  title?: string;
}

const DemoScreen: React.FC<DemoScreenProps> = ({ title }) => {
  useEffect(() => {
    if (title) {
      document.title = title;
    }
  }, [title]);

  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);

  const handleOpenAddCategoryModal = () => {
    setIsAddCategoryModalOpen(true);
  };

  const handleCloseAddCategoryModal = () => {
    setIsAddCategoryModalOpen(false);
  };

  const handleAddCategory = (categoryName: string) => {
    console.log("Added category:", categoryName);
    setIsAddCategoryModalOpen(false);
  };

  return (
    <Box sx={{ p: 3, maxWidth: "800px", margin: "0 auto" }}>
      <Typography variant="h4" gutterBottom>
        Business Category Demo
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        This is a demo page to showcase the business category modal
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Demo Controls
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleOpenAddCategoryModal}
              sx={{ textTransform: "none", mr: 2 }}
            >
              Open Add Business Category Modal
            </Button>
          </Box>
        </CardContent>
      </Card>

      <AddBusinessCategoryModal
        open={isAddCategoryModalOpen}
        onClose={handleCloseAddCategoryModal}
        onAddCategory={handleAddCategory}
      />
    </Box>
  );
};

export default DemoScreen;
