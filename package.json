{"name": "mylocobiz", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fontsource/barlow": "^5.2.5", "@fontsource/roboto": "^5.1.0", "@mui/icons-material": "^6.1.9", "@mui/material": "^6.1.9", "@mui/x-date-pickers": "^7.24.1", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.121", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/redux": "^3.6.0", "axios": "^1.7.8", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "dayjs": "^1.11.13", "deep-equal": "^2.2.3", "formik": "^2.4.6", "html2canvas": "^1.4.1", "moment": "^2.30.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-loader-spinner": "^6.1.6", "react-mui-scheduler": "^2.0.4", "react-pick-color": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^7.0.1", "react-scripts": "^5.0.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.5.0"}, "scripts": {"start": "env-cmd -f --environments local react-scripts start", "build-prod": "env-cmd -f --environments production react-scripts build", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-redux": "^7.1.25", "ajv": "^7.2.4", "env-cmd": "^10.1.0"}, "overrides": {"nth-check": "^2.1.1"}}