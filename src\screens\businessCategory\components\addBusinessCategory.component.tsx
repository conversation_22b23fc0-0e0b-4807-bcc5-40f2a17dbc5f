import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  AppBar,
  Toolbar,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

interface AddBusinessCategoryModalProps {
  open: boolean;
  onClose: () => void;
  onAddCategory: (categoryName: string) => void;
}

const AddBusinessCategoryModal: React.FC<AddBusinessCategoryModalProps> = ({
  open,
  onClose,
  onAddCategory,
}) => {
  const [categoryName, setCategoryName] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = () => {
    if (!categoryName.trim()) {
      setError("Please enter a business category");
      return;
    }
    onAddCategory(categoryName);
    setCategoryName("");
    setError("");
  };

  const handleCancel = () => {
    setCategoryName("");
    setError("");
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        style: {
          backgroundColor: "#212121",
          color: "white",
          borderRadius: "8px",
        },
      }}
    >
      <AppBar position="relative" color="transparent" elevation={0}>
        <Toolbar sx={{ minHeight: "56px" }}>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleCancel}
            aria-label="back"
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{ flexGrow: 1, ml: 1 }}
          >
            Add business category
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCancel}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent>
        <Typography variant="body2" sx={{ mb: 2 }}>
          Find new customers by adding a new business category
        </Typography>
        <TextField
          autoFocus
          margin="dense"
          id="category"
          label="Enter a business category"
          type="text"
          fullWidth
          variant="outlined"
          value={categoryName}
          onChange={(e) => {
            setCategoryName(e.target.value);
            if (e.target.value.trim()) {
              setError("");
            }
          }}
          error={!!error}
          helperText={error}
          InputProps={{
            style: { color: "white" },
          }}
          InputLabelProps={{
            style: { color: "#aaa" },
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: "rgba(255, 255, 255, 0.23)",
              },
              "&:hover fieldset": {
                borderColor: "rgba(255, 255, 255, 0.5)",
              },
              "&.Mui-focused fieldset": {
                borderColor: "#90caf9",
              },
            },
          }}
        />
      </DialogContent>

      <Box sx={{ flexGrow: 1 }} />

      <DialogActions sx={{ p: 2 }}>
        <Button
          onClick={handleCancel}
          sx={{
            color: "#90caf9",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "rgba(144, 202, 249, 0.08)",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            bgcolor: "#90caf9",
            color: "#000",
            textTransform: "none",
            "&:hover": {
              bgcolor: "#64b5f6",
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddBusinessCategoryModal;
