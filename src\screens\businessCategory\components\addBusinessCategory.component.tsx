import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  AppBar,
  Toolbar,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

interface AddBusinessCategoryModalProps {
  open: boolean;
  onClose: () => void;
  onAddCategory: (categoryName: string) => void;
}

const AddBusinessCategoryModal: React.FC<AddBusinessCategoryModalProps> = ({
  open,
  onClose,
  onAddCategory,
}) => {
  const [categoryName, setCategoryName] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = () => {
    if (!categoryName.trim()) {
      setError("Please enter a business category");
      return;
    }
    onAddCategory(categoryName);
    setCategoryName("");
    setError("");
  };

  const handleCancel = () => {
    setCategoryName("");
    setError("");
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        style: {
          backgroundColor: "#212121",
          color: "white",
          borderRadius: "8px",
        },
      }}
      sx={{
        "& .MuiDialog-paper": {
          margin: { xs: "16px", sm: "32px" },
          width: { xs: "calc(100% - 32px)", sm: "auto" },
          maxHeight: { xs: "calc(100% - 32px)", sm: "auto" },
        },
      }}
    >
      <AppBar position="relative" color="transparent" elevation={0}>
        <Toolbar
          sx={{
            minHeight: { xs: "48px", sm: "56px" },
            px: { xs: 1, sm: 2 },
          }}
        >
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleCancel}
            aria-label="back"
            sx={{ mr: { xs: 0.5, sm: 1 } }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              ml: 1,
              fontSize: { xs: "1rem", sm: "1.25rem" },
            }}
          >
            Add business category
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCancel}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 } }}>
        <Typography
          variant="body2"
          sx={{
            mb: 2,
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
          }}
        >
          Find new customers by adding a new business category
        </Typography>
        <TextField
          autoFocus
          margin="dense"
          id="category"
          label="Enter a business category"
          type="text"
          fullWidth
          variant="outlined"
          value={categoryName}
          onChange={(e) => {
            setCategoryName(e.target.value);
            if (e.target.value.trim()) {
              setError("");
            }
          }}
          error={!!error}
          helperText={error}
          InputProps={{
            style: { color: "white" },
          }}
          InputLabelProps={{
            style: { color: "#aaa" },
            sx: {
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
            },
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: "rgba(255, 255, 255, 0.23)",
              },
              "&:hover fieldset": {
                borderColor: "rgba(255, 255, 255, 0.5)",
              },
              "&.Mui-focused fieldset": {
                borderColor: "#90caf9",
              },
              fontSize: { xs: "0.875rem", sm: "1rem" },
            },
            "& .MuiFormHelperText-root": {
              color: "#f44336",
              marginLeft: 0,
              fontSize: { xs: "0.7rem", sm: "0.75rem" },
            },
          }}
        />
      </DialogContent>

      <Box sx={{ flexGrow: 1 }} />

      <DialogActions
        sx={{
          p: { xs: 1.5, sm: 2 },
          justifyContent: "space-between",
        }}
      >
        <Button
          onClick={handleCancel}
          sx={{
            color: "#90caf9",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            "&:hover": {
              backgroundColor: "rgba(144, 202, 249, 0.08)",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            bgcolor: "#90caf9",
            color: "#000",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            px: { xs: 2, sm: 3 },
            "&:hover": {
              bgcolor: "#64b5f6",
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddBusinessCategoryModal;
