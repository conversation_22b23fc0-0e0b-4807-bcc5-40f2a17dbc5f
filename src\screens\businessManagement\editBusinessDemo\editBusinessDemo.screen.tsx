import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Divider,
  TextField,
  Grid,
  Paper,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import EditBusinessNameModal from "../../../components/editBusinessName/editBusinessName.component";
import { useDispatch, useSelector } from "react-redux";
import BusinessService from "../../../services/business/business.service";
import { ToastContext } from "../../../context/toast.context";
import { LoadingContext } from "../../../context/loading.context";
import { IBusiness } from "../../../interfaces/response/IBusinessListResponseModel";

interface EditBusinessDemoScreenProps {
  title?: string;
}

const EditBusinessDemoScreen: React.FC<EditBusinessDemoScreenProps> = ({ title }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [businessData, setBusinessData] = useState<IBusiness | null>(null);
  const [businessName, setBusinessName] = useState<string>("Sri Eye Care Hospital - Best Ophthalmologist in RT Nagar | Best Cataract Surgeon in Bangalore");
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setLoading } = React.useContext(LoadingContext);
  const { setToastConfig } = React.useContext(ToastContext);

  useEffect(() => {
    if (title) {
      document.title = title;
    }
  }, [title]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSuccess = () => {
    // In a real app, we would fetch the updated business data
    // For demo purposes, we'll just update the local state
    setBusinessName(businessData?.businessName || "");
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 }, maxWidth: "1200px", margin: "0 auto" }}>
      <Typography 
        variant="h4" 
        gutterBottom
        sx={{ 
          fontSize: { xs: "1.5rem", sm: "2.125rem" },
          fontWeight: 600,
          mb: 2
        }}
      >
        Edit Business Demo
      </Typography>
      
      <Typography 
        variant="body1" 
        color="textSecondary" 
        paragraph
        sx={{ mb: 4 }}
      >
        This demo shows how to edit a business name with a modal popup.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card 
            sx={{ 
              mb: 4, 
              bgcolor: "white",
              boxShadow: "0px 2px 10px rgba(0, 0, 0, 0.08)",
              borderRadius: "8px",
              overflow: "hidden"
            }}
          >
            <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
              <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    fontWeight: 600,
                    color: "black",
                    fontSize: { xs: "1.1rem", sm: "1.25rem" }
                  }}
                >
                  Business Information
                </Typography>
                
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={handleOpenModal}
                  sx={{
                    textTransform: "none",
                    borderColor: "primary.main",
                    color: "primary.main",
                    "&:hover": {
                      borderColor: "primary.dark",
                      backgroundColor: "rgba(63, 81, 181, 0.04)",
                    },
                  }}
                >
                  Edit Business
                </Button>
              </Box>
              
              <Divider sx={{ mb: 3 }} />
              
              <Box sx={{ mb: 3 }}>
                <Typography 
                  variant="subtitle2" 
                  gutterBottom
                  sx={{ 
                    color: "black", 
                    fontWeight: 600,
                    fontSize: { xs: "0.875rem", sm: "1rem" }
                  }}
                >
                  Business name
                </Typography>
                
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    backgroundColor: "#f5f5f5",
                    borderRadius: "4px",
                    border: "1px solid rgba(0, 0, 0, 0.12)",
                  }}
                >
                  <Typography 
                    variant="body1"
                    sx={{ 
                      color: "black",
                      wordBreak: "break-word",
                      fontSize: { xs: "0.875rem", sm: "1rem" }
                    }}
                  >
                    {businessName}
                  </Typography>
                </Paper>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography 
                  variant="body2" 
                  color="textSecondary"
                  sx={{ fontSize: { xs: "0.75rem", sm: "0.875rem" } }}
                >
                  Last updated: {new Date().toLocaleDateString()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card 
            sx={{ 
              bgcolor: "white",
              boxShadow: "0px 2px 10px rgba(0, 0, 0, 0.08)",
              borderRadius: "8px"
            }}
          >
            <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
              <Typography 
                variant="h6" 
                gutterBottom
                sx={{ 
                  fontWeight: 600,
                  color: "black",
                  fontSize: { xs: "1rem", sm: "1.125rem" },
                  mb: 2
                }}
              >
                Instructions
              </Typography>
              
              <Typography 
                variant="body2" 
                paragraph
                sx={{ 
                  color: "rgba(0, 0, 0, 0.7)",
                  fontSize: { xs: "0.8125rem", sm: "0.875rem" }
                }}
              >
                Click the "Edit Business" button to open a modal popup where you can edit the business name.
              </Typography>
              
              <Typography 
                variant="body2"
                sx={{ 
                  color: "rgba(0, 0, 0, 0.7)",
                  fontSize: { xs: "0.8125rem", sm: "0.875rem" }
                }}
              >
                The modal includes:
              </Typography>
              
              <ul style={{ paddingLeft: "20px", marginTop: "8px" }}>
                <li>
                  <Typography 
                    variant="body2"
                    sx={{ 
                      color: "rgba(0, 0, 0, 0.7)",
                      fontSize: { xs: "0.8125rem", sm: "0.875rem" }
                    }}
                  >
                    Form validation with Formik and Yup
                  </Typography>
                </li>
                <li>
                  <Typography 
                    variant="body2"
                    sx={{ 
                      color: "rgba(0, 0, 0, 0.7)",
                      fontSize: { xs: "0.8125rem", sm: "0.875rem" }
                    }}
                  >
                    Responsive design for all screen sizes
                  </Typography>
                </li>
                <li>
                  <Typography 
                    variant="body2"
                    sx={{ 
                      color: "rgba(0, 0, 0, 0.7)",
                      fontSize: { xs: "0.8125rem", sm: "0.875rem" }
                    }}
                  >
                    White background with black labels
                  </Typography>
                </li>
              </ul>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Edit Business Name Modal */}
      <EditBusinessNameModal
        open={isModalOpen}
        onClose={handleCloseModal}
        businessId={1} // Demo business ID
        currentBusinessName={businessName}
        onSuccess={() => {
          // In a real app, we would fetch the updated business data
          // For demo purposes, we'll just update the local state with the form value
          setBusinessName(document.getElementById("businessName")?.value || businessName);
        }}
      />
    </Box>
  );
};

export default EditBusinessDemoScreen;
