import React, { useEffect, useState } from "react";
import {
  Box,
  Menu,
  Radio,
  RadioGroup,
  FormControlLabel,
  Typography,
  FilledInput,
  InputLabel,
  FormControl,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import dayjs, { Dayjs } from "dayjs";

interface Props {
  onDateChange: (range: {
    from: string;
    to: string;
    isSameMonthYear: boolean;
  }) => void;
}

const durationLabels: Record<string, string> = {
  all: "All Time",
  "7": "Past 7 days",
  "30": "Past 30 days",
  "90": "Past 90 days",
  "180": "Past 180 days",
  custom: "Pick Date Range",
};

const DateFilter: React.FC<Props> = ({ onDateChange }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDuration, setSelectedDuration] = useState("all");
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);
  const [dateError, setDateError] = useState<string | null>(null);

  const open = Boolean(anchorEl);
  const isCustomRange = selectedDuration === "custom";

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setTimeout(() => handleApply(), 100); // Delay to avoid conflict with selection events
  };

  useEffect(() => {
    console.log(selectedDuration);
    handleDurationChange(selectedDuration);
    setTimeout(() => {
      onDateChange({
        from: dayjs().subtract(1, "year").format("YYYY-MM-DD"),
        to: dayjs().format("YYYY-MM-DD"),
        isSameMonthYear: false,
      });
    }, 1000);
  }, []);

  const handleDurationChange = (value: string) => {
    setSelectedDuration(value);
    setDateError(null);

    const end = dayjs();
    let start: Dayjs | null = null;

    switch (value) {
      case "7":
        start = end.subtract(7, "day");
        break;
      case "30":
        start = end.subtract(30, "day");
        break;
      case "90":
        start = end.subtract(90, "day");
        break;
      case "180":
        start = end.subtract(180, "day");
        break;
      case "all":
        start = dayjs().subtract(1, "year"); // Arbitrary wide range
        break;
      case "custom":
        setFromDate(null);
        setToDate(null);
        return;
    }

    setFromDate(start);
    setToDate(end);
  };

  const validateDates = () => {
    if (isCustomRange && fromDate && toDate && fromDate.isAfter(toDate)) {
      setDateError("From date cannot be after To date.");
      return false;
    }
    setDateError(null);
    return true;
  };

  const handleApply = () => {
    if (!validateDates()) return;

    if (fromDate && toDate) {
      // Check if month and year are the same
      if (
        fromDate.year() === toDate.year() &&
        fromDate.month() === toDate.month() &&
        selectedDuration === "custom"
      ) {
        const startOfMonth = fromDate.startOf("month");
        const endOfMonth = fromDate.endOf("month");
        onDateChange({
          from: startOfMonth.format("YYYY-MM-DD"),
          to: endOfMonth.format("YYYY-MM-DD"),
          isSameMonthYear: true,
        });
      } else {
        onDateChange({
          from: fromDate.format("YYYY-MM-DD"),
          to: toDate.format("YYYY-MM-DD"),
          isSameMonthYear: false,
        });
      }
    }
    setAnchorEl(null);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <FormControl variant="filled" fullWidth>
        <InputLabel shrink htmlFor="filled-select-date">
          Duration
        </InputLabel>
        <FilledInput
          id="filled-select-date"
          disableUnderline
          onClick={handleClick}
          value={durationLabels[selectedDuration]}
          endAdornment={<ArrowDropDownIcon />}
          sx={{
            backgroundColor: "#ffffff",
            borderRadius: "8px",
            paddingRight: 2,
            cursor: "pointer",
          }}
          readOnly
        />
      </FormControl>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            p: 2,
            width: 270,
          },
        }}
      >
        <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
          Duration
        </Typography>

        <RadioGroup
          value={selectedDuration}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
            handleDurationChange(event.target.value)
          }
        >
          {Object.entries(durationLabels).map(([value, label]) => (
            <FormControlLabel
              key={value}
              value={value}
              control={<Radio />}
              label={label}
            />
          ))}
        </RadioGroup>

        {isCustomRange && (
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <DatePicker
              label="From"
              value={fromDate}
              onChange={(newValue) => setFromDate(newValue)}
              views={["year", "month"]} // Only show year and month
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  error: Boolean(dateError),
                },
              }}
            />
            <DatePicker
              label="To"
              value={toDate}
              minDate={fromDate || undefined}
              onChange={(newValue) => setToDate(newValue)}
              views={["year", "month"]} // Only show year and month
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  error: Boolean(dateError),
                  helperText: dateError,
                },
              }}
            />
          </Box>
        )}
      </Menu>
    </LocalizationProvider>
  );
};

export default DateFilter;
