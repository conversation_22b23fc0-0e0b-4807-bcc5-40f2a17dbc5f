import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  useMediaQuery,
  useTheme,
  Link,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Formik, Form, FormikHelpers } from "formik";
import * as yup from "yup";
import { useDispatch, useSelector } from "react-redux";
import BusinessService from "../../services/business/business.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";

interface EditBusinessNameProps {
  open: boolean;
  onClose: () => void;
  businessId: number;
  currentBusinessName: string;
  onSuccess?: () => void;
}

interface BusinessNameFormValues {
  businessName: string;
}

const EditBusinessNameModal: React.FC<EditBusinessNameProps> = ({
  open,
  onClose,
  businessId,
  currentBusinessName,
  onSuccess,
}) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setToastConfig } = React.useContext(ToastContext);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validationSchema = yup.object({
    businessName: yup
      .string()
      .required("Business name is required")
      .min(3, "Business name must be at least 3 characters")
      .max(100, "Business name must be at most 100 characters"),
  });

  const initialValues: BusinessNameFormValues = {
    businessName: currentBusinessName || "",
  };

  const handleSubmit = async (
    values: BusinessNameFormValues,
    { setSubmitting }: FormikHelpers<BusinessNameFormValues>
  ) => {
    try {
      setIsSubmitting(true);
      const _businessService = new BusinessService(dispatch);
      
      // Create the request model with all required fields
      const requestModel = {
        businessName: values.businessName,
        businessEmail: "", // This will be filled by the backend from existing data
        userId: 0, // This will be filled by the backend from existing data
        statusId: 1,
        createdBy: userInfo.id,
        updatedBy: userInfo.id,
      };
      
      await _businessService.updateBusiness(requestModel, businessId);
      
      setToastConfig(
        ToastSeverity.Success,
        MessageConstants.BusinessUpdatedSuccessfully,
        true
      );
      
      if (onSuccess) {
        onSuccess();
      }
      
      onClose();
    } catch (error) {
      console.error("Error updating business name:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update business name. Please try again.",
        true
      );
    } finally {
      setSubmitting(false);
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      fullScreen={fullScreen}
      PaperProps={{
        style: {
          backgroundColor: "white",
          borderRadius: "8px",
          maxWidth: fullScreen ? "100%" : "500px",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: { xs: "16px", sm: "20px 24px" },
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        <Typography
          variant="h6"
          component="div"
          sx={{
            fontWeight: 600,
            color: "black",
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
          }}
        >
          Business name
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{ color: "rgba(0, 0, 0, 0.54)" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          isValid,
          dirty,
        }) => (
          <Form>
            <DialogContent
              sx={{
                padding: { xs: "16px", sm: "24px" },
                backgroundColor: "white",
              }}
            >
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{
                  mb: 2,
                  color: "rgba(0, 0, 0, 0.6)",
                  fontSize: { xs: "0.875rem", sm: "1rem" },
                }}
              >
                Enter your business name as it appears to customers in the real world.{" "}
                <Link href="#" color="primary" underline="always">
                  Learn more
                </Link>
              </Typography>

              <TextField
                autoFocus
                margin="dense"
                id="businessName"
                name="businessName"
                fullWidth
                variant="outlined"
                value={values.businessName}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.businessName && Boolean(errors.businessName)}
                helperText={touched.businessName && errors.businessName}
                sx={{
                  mt: 2,
                  "& .MuiOutlinedInput-root": {
                    "& fieldset": {
                      borderColor: "rgba(0, 0, 0, 0.23)",
                    },
                    "&:hover fieldset": {
                      borderColor: "rgba(0, 0, 0, 0.5)",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "primary.main",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "black",
                  },
                  "& .MuiInputBase-input": {
                    color: "black",
                    padding: { xs: "12px 14px", sm: "16px 14px" },
                    fontSize: { xs: "0.9rem", sm: "1rem" },
                  },
                  "& .MuiFormHelperText-root": {
                    marginLeft: 0,
                  },
                }}
                InputProps={{
                  style: { backgroundColor: "white" },
                }}
              />
            </DialogContent>

            <DialogActions
              sx={{
                padding: { xs: "16px", sm: "16px 24px 24px" },
                justifyContent: "space-between",
                borderTop: "1px solid rgba(0, 0, 0, 0.12)",
              }}
            >
              <Button
                onClick={onClose}
                sx={{
                  color: "primary.main",
                  textTransform: "none",
                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  fontWeight: 500,
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={!isValid || !dirty || isSubmitting}
                sx={{
                  bgcolor: "primary.main",
                  color: "white",
                  textTransform: "none",
                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  fontWeight: 500,
                  px: { xs: 2, sm: 3 },
                  py: { xs: 0.75, sm: 1 },
                  "&:hover": {
                    bgcolor: "primary.dark",
                  },
                  "&.Mui-disabled": {
                    bgcolor: "rgba(0, 0, 0, 0.12)",
                    color: "rgba(0, 0, 0, 0.26)",
                  },
                }}
              >
                Save
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default EditBusinessNameModal;
