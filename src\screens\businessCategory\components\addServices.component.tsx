import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  AppBar,
  Toolbar,
  InputAdornment,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";

interface AddServicesModalProps {
  open: boolean;
  onClose: () => void;
  categoryName: string;
  isPrimary: boolean;
  onAddService: (serviceName: string) => void;
}

const AddServicesModal: React.FC<AddServicesModalProps> = ({
  open,
  onClose,
  categoryName,
  isPrimary,
  onAddService,
}) => {
  const [showCustomServiceInput, setShowCustomServiceInput] = useState(false);
  const [customServiceName, setCustomServiceName] = useState("");
  const [error, setError] = useState("");

  const handleAddCustomService = () => {
    setShowCustomServiceInput(true);
  };

  const handleCancelCustomService = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
  };

  const handleSubmit = () => {
    if (showCustomServiceInput) {
      if (!customServiceName.trim()) {
        setError("Please enter a service name");
        return;
      }
      onAddService(customServiceName);
      setCustomServiceName("");
      setError("");
      setShowCustomServiceInput(false);
    }
    onClose();
  };

  const handleCancel = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        style: {
          backgroundColor: "#212121",
          color: "white",
          borderRadius: "8px",
        },
      }}
    >
      <AppBar position="relative" color="transparent" elevation={0}>
        <Toolbar sx={{ minHeight: "56px" }}>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleCancel}
            aria-label="back"
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{ flexGrow: 1, ml: 1 }}
          >
            Add services
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCancel}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6">{categoryName}</Typography>
          <Typography variant="caption" color="text.secondary">
            {isPrimary ? "Primary category" : "Additional category"}
          </Typography>
        </Box>

        <Box sx={{ mt: 3, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Don't see a service you offer? Create your own
          </Typography>
        </Box>

        {showCustomServiceInput ? (
          <Box sx={{ mb: 2 }}>
            <TextField
              autoFocus
              margin="dense"
              fullWidth
              variant="outlined"
              value={customServiceName}
              onChange={(e) => {
                setCustomServiceName(e.target.value);
                if (e.target.value.trim()) {
                  setError("");
                }
              }}
              error={!!error}
              helperText={error}
              InputProps={{
                style: { color: "white" },
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleCancelCustomService}
                      edge="end"
                      sx={{ color: "white" }}
                    >
                      <CancelIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              InputLabelProps={{
                style: { color: "#aaa" },
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.23)",
                  },
                  "&:hover fieldset": {
                    borderColor: "rgba(255, 255, 255, 0.5)",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#90caf9",
                  },
                },
              }}
            />
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: "block", textAlign: "right" }}
            >
              {customServiceName.length}/120
            </Typography>
          </Box>
        ) : (
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddCustomService}
            sx={{
              color: "#90caf9",
              textTransform: "none",
              justifyContent: "flex-start",
              padding: "8px 0",
            }}
          >
            Add custom service
          </Button>
        )}
      </DialogContent>

      <Box sx={{ flexGrow: 1 }} />

      <DialogActions sx={{ p: 2 }}>
        <Button
          onClick={handleCancel}
          sx={{
            color: "#90caf9",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "rgba(144, 202, 249, 0.08)",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={showCustomServiceInput && !customServiceName.trim()}
          sx={{
            bgcolor: "#90caf9",
            color: "#000",
            textTransform: "none",
            "&:hover": {
              bgcolor: "#64b5f6",
            },
            "&.Mui-disabled": {
              bgcolor: "rgba(144, 202, 249, 0.3)",
              color: "rgba(0, 0, 0, 0.5)",
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddServicesModal;
