import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Button,
  Typography,
  Card,
  CardContent,
  Divider,
  IconButton,
} from "@mui/material";
import AddBusinessCategoryModal from "./components/addBusinessCategory.component";
import ServicesDisplay from "./components/servicesDisplay.component";
import AddIcon from "@mui/icons-material/Add";

interface BusinessCategoryScreenProps {
  title?: string;
}

const BusinessCategoryScreen: React.FC<BusinessCategoryScreenProps> = ({
  title,
}) => {
  useEffect(() => {
    if (title) {
      document.title = title;
    }
  }, [title]);
  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);
  const [categories, setCategories] = useState<
    Array<{
      name: string;
      isPrimary: boolean;
      services: Array<{
        name: string;
        description: string;
      }>;
    }>
  >([
    {
      name: "Eye Care Clinic",
      isPrimary: true,
      services: [
        {
          name: "Cataract Eye Treatment",
          description:
            "At Sri Eye Care, we offer advanced cataract eye treatment. Our ophthalmologists use advanced...",
        },
        {
          name: "Photorefractive Keratectomy",
          description:
            "At Sri Eye Care, we use painless techniques and advanced equipment for photorefractive keratectomy...",
        },
      ],
    },
    {
      name: "Paediatric Ophthalmologist",
      isPrimary: false,
      services: [],
    },
  ]);

  const handleOpenAddCategoryModal = () => {
    setIsAddCategoryModalOpen(true);
  };

  const handleCloseAddCategoryModal = () => {
    setIsAddCategoryModalOpen(false);
  };

  const handleAddCategory = (categoryName: string) => {
    const newCategory = {
      name: categoryName,
      isPrimary: categories.length === 0, // First category is primary
      services: [],
    };

    setCategories([...categories, newCategory]);
    setIsAddCategoryModalOpen(false);
  };

  return (
    <Box
      sx={{
        p: { xs: 2, sm: 3 },
        maxWidth: "800px",
        margin: "0 auto",
      }}
    >
      <Typography
        variant="h4"
        gutterBottom
        sx={{
          fontSize: { xs: "1.5rem", sm: "2.125rem" },
          wordBreak: "break-word",
        }}
      >
        Business Categories
      </Typography>
      <Typography
        variant="body1"
        color="text.secondary"
        paragraph
        sx={{ fontSize: { xs: "0.875rem", sm: "1rem" } }}
      >
        Manage your business categories and services
      </Typography>

      <Card
        sx={{
          mb: 4,
          bgcolor: "#f5f5f5",
          borderRadius: { xs: "8px", sm: "12px" },
          boxShadow: {
            xs: "0 1px 3px rgba(0,0,0,0.1)",
            sm: "0 2px 5px rgba(0,0,0,0.1)",
          },
        }}
      >
        <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ fontSize: { xs: "1rem", sm: "1.25rem" } }}
          >
            Services
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <ServicesDisplay
            categories={categories}
            onUpdateCategories={setCategories}
          />

          <Box
            sx={{
              mt: { xs: 2, sm: 3 },
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleOpenAddCategoryModal}
              sx={{
                textTransform: "none",
                fontSize: { xs: "0.8rem", sm: "0.875rem" },
                py: { xs: 0.75, sm: 1 },
                px: { xs: 1.5, sm: 2 },
                "& .MuiButton-startIcon": {
                  mr: { xs: 0.5, sm: 1 },
                },
              }}
            >
              Add another business category
            </Button>
          </Box>
        </CardContent>
      </Card>

      <AddBusinessCategoryModal
        open={isAddCategoryModalOpen}
        onClose={handleCloseAddCategoryModal}
        onAddCategory={handleAddCategory}
      />
    </Box>
  );
};

export default BusinessCategoryScreen;
